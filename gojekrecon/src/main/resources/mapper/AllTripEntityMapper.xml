<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdgtaxi.recon.mappers.AllTripEntityMapper">

    <!-- Result map for RCTBTripEntity -->
    <resultMap id="RCTBTripEntityResultMap" type="com.cdgtaxi.recon.inbound.entities.RCTBTripEntity">
        <result column="booking_reference" property="bookingReference"/>
        <result column="job_no" property="jobNo"/>
        <result column="vehicle_no" property="vehicleNo"/>
        <result column="job_status" property="jobStatus"/>
        <result column="entity" property="entity"/>
        <result column="booked_channel" property="bookedChannel"/>
        <result column="completed_dt" property="completedDt"/>
        <result column="payment_mode" property="paymentMode"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="driver_levy" property="driverLevy"/>
        <result column="refund" property="refund"/>
        <result column="calc_driver_refund" property="calcDriverRefund"/>
        <result column="dcp_request_id" property="dcpRequestId"/>
        <result column="difference" property="difference"/>
        <result column="remarks_all" property="remarksAll"/>
    </resultMap>

    <!-- Find all trips by start and end time -->
    <select id="findAllTripByStartEndTime" resultMap="RCTBTripEntityResultMap">
        select t.booking_reference,
               t.job_no,
               t.vehicle_no,
               t.job_status,
               t.entity,
               t.booked_channel,
               TO_CHAR(t.completed_dt, 'YYYY-MM-DD') as completed_dt,
               t.payment_mode,
               t.total_amount,
               t.driver_levy,
               t.refund,
               (t.total_amount-t.driver_levy) as calc_driver_refund,
               ej.dcp_request_id as dcp_request_id,
               'M2-AA2 (payment_to_cdg - total_amount)' as difference,
               '' as REMARKS_ALL
        from recsys.rctb_trip_intf t
        LEFT JOIN recsys.ESC_JOB ej ON t.JOB_NO = ej.JOB_NO
        WHERE 1=1
        and t.booked_channel='GOJEK'
        and t.job_status='COMPLETED'
        and t.completed_dt>=to_date(#{startDT},'yyyymmdd hh24:mi:ss')
        and t.completed_dt<=to_date(#{endDT},'yyyymmdd hh24:mi:ss')
    </select>

</mapper>
