spring.application.name=recon
# jetty port configuration
server.port=9901

# Exclude default DataSource auto-configuration since we're using dynamic datasource
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration

#
#spring.mvc.view.prefix=
#spring.mvc.view.suffix=.html
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html


# file upload configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB


server.error.whitelabel.enable=false


# Dynamic DataSource Configuration
spring.datasource.dynamic.primary=master
spring.datasource.dynamic.strict=false
spring.datasource.dynamic.datasource.master.driver-class-name=oracle.jdbc.OracleDriver
spring.datasource.dynamic.datasource.master.type=com.zaxxer.hikari.HikariDataSource
# Master DataSource HikariCP settings
spring.datasource.dynamic.datasource.master.hikari.minimum-idle=5
spring.datasource.dynamic.datasource.master.hikari.maximum-pool-size=30
spring.datasource.dynamic.datasource.master.hikari.idle-timeout=30000
spring.datasource.dynamic.datasource.master.hikari.max-lifetime=2000000
spring.datasource.dynamic.datasource.master.hikari.connection-timeout=30000

# MyBatis Plus configuration
mybatis-plus.mapper-locations=classpath:mapper/*.xml
mybatis-plus.type-aliases-package=com.cdgtaxi.recon.inbound.entities
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# Enable logging to verify that HikariCP is used
logging.level.com.zaxxer.hikari.HikariConfig=DEBUG
logging.level.org.springframework.boot.devtools=DEBUG

fileLoader.upload.dir=/home/<USER>/Documents/gojek/uploads
fileLoader.download.dir=/home/<USER>/Documents/gojek/downloads


#
## Master Database configuration - UAT1 (Default)
#spring.datasource.dynamic.datasource.master.url=******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptfsuat1.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptfsuat1_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
#spring.datasource.dynamic.datasource.master.username=recuser
#spring.datasource.dynamic.datasource.master.password=ociUsr$rec2022
#
## Secondary Database configuration - RECSYS (for trip data)
#spring.datasource.dynamic.datasource.recsys.driver-class-name=oracle.jdbc.OracleDriver
#spring.datasource.dynamic.datasource.recsys.type=com.zaxxer.hikari.HikariDataSource
#spring.datasource.dynamic.datasource.recsys.url=******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptfsuat1.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptfsuat1_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
#spring.datasource.dynamic.datasource.recsys.username=recuser
#spring.datasource.dynamic.datasource.recsys.password=ociUsr$rec2022
#spring.datasource.dynamic.datasource.recsys.hikari.minimum-idle=3
#spring.datasource.dynamic.datasource.recsys.hikari.maximum-pool-size=20
#
## Archive Database configuration - for historical data
#spring.datasource.dynamic.datasource.archive.driver-class-name=oracle.jdbc.OracleDriver
#spring.datasource.dynamic.datasource.archive.type=com.zaxxer.hikari.HikariDataSource
#spring.datasource.dynamic.datasource.archive.url=******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptfsuat1.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptfsuat1_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
#spring.datasource.dynamic.datasource.archive.username=recuser
#spring.datasource.dynamic.datasource.archive.password=ociUsr$rec2022

#springdoc.show.actuator=true

springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.path=/swagger-ui.html