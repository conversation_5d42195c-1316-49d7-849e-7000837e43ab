spring.application.name=recon
# jetty port configuration
server.port=9901

#
#spring.mvc.view.prefix=
#spring.mvc.view.suffix=.html
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html


# file upload configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB


server.error.whitelabel.enable=false


spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
spring.datasource.type=com.zaxxer.hikari.HikariDataSource

# HikariCP settings
spring.datasource.hikari.minimumIdle=5
spring.datasource.hikari.maximumPoolSize=30
spring.datasource.hikari.idleTimeout=30000
spring.datasource.hikari.maxLifetime=2000000
spring.datasource.hikari.connectionTimeout=30000
spring.datasource.hikari.poolName=paymentServicePoolSources

spring.jpa.hibernate.connection.provider_class=org.hibernate.hikaricp.internal.HikariCPConnectionProvider

# JPA specific configs
spring.jpa.properties.hibernate.show_sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.use_sql=false
spring.jpa.properties.hibernate.id.new_generator_mappings=false
spring.jpa.properties.hibernate.search.autoregister_listeners=false
spring.jpa.properties.hibernate.bytecode.use_reflection_optimizer=false

# Enable logging to verify that HikariCP is used, the second entry is specific to HikariCP
logging.level.org.hibernate.SQL=DEBUG
logging.level.com.zaxxer.hikari.HikariConfig=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

logging.level.org.springframework.boot.devtools=DEBUG

fileLoader.upload.dir=/home/<USER>/Documents/gojek/uploads
fileLoader.download.dir=/home/<USER>/Documents/gojek/downloads



#Database configuration - UAT1
spring.datasource.url=******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptfsuat1.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptfsuat1_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
spring.datasource.username=recuser
#spring.datasource.password=ociUsr$rec2022
#spring.datasource.password=ENC(ZhiGGkjvp32rX8MgDoQbyqmRl5qJ6WYa)

#springdoc.show.actuator=true

springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.path=/swagger-ui.html