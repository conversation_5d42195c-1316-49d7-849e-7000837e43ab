spring.application.name=recon
# jetty port configuration
server.port=9901
server.error.whitelabel.enable=false

##contextPath configuration
#server.servlet.contextPath=/gojekrecon

#Database configuration - UAT1
spring.datasource.url=******************************= (retry_count=2)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptfsuat1.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptfsuat1_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
spring.datasource.username=recuser
#spring.datasource.password=ociUsr$rec2022
spring.datasource.password=ENC(ZhiGGkjvp32rX8MgDoQbyqmRl5qJ6WYa)


spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html