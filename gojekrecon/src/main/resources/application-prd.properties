spring.application.name=recon
# jetty port configuration
server.port=9901
server.error.whitelabel.enable=false

##contextPath configuration
#server.servlet.contextPath=/gojekrecon

#Database configuration - prod
spring.datasource.url=******************************= (retry_count=2)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptfspro.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptfspro_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
spring.datasource.username=CDG_DENG_HUI
spring.datasource.password=ENC(e5Won+aibmeuM1G6ORilImvV6Yws90393lZgB+4/4jk=)
