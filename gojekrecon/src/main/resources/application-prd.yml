spring:
  datasource:
    dynamic:
      primary: master  # default datasource
      strict: true    # error if can not find datasource
      datasource:
        master:
          url: ******************************= (retry_count=2)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptfspro.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptfspro_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
          username: CDG_DENG_HUI
          password: Cdg0000278_02
          driver-class-name: oracle.jdbc.OracleDriver
          hikari:
            connection-init-sql: ALTER SESSION SET CURRENT_SCHEMA=recsys

        ptdapro:
          url: ******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptdapro.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptdapro_low.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
          username: CDG_DENG_HUI
          password: Cdg0000278_02
          driver-class-name: oracle.jdbc.OracleDriver

mybatis-plus:
  mapper-locations: classpath:/mapper/**/*.xml
  type-aliases-package: com.cdgtaxi.recon.inbound.entities
  global-config:
    db-config:
      id-type: auto


logging:
  file:
    path: "/home/<USER>/codes/cdg-recon-cabcharge-nstljoblogs/"