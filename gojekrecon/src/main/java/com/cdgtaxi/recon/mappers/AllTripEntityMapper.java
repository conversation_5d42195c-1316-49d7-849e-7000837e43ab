package com.cdgtaxi.recon.mappers;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdgtaxi.recon.inbound.entities.RCTBTripEntity;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
@DS("recsys") // Use recsys datasource for trip data
public interface AllTripEntityMapper extends BaseMapper<RCTBTripEntity> {

  /**
   * Find all trips by start and end time This replaces the JPA native query
   * findAllTripByStartEndTime
   */
  List<RCTBTripEntity> findAllTripByStartEndTime(
      @Param("startDT") String startDT, @Param("endDT") String endDT);

  /**
   * Find all trips by CDG trip IDs in previous weeks This replaces the JPA native query
   * findAllTripByCdgTripIdsInPreviousWeeks Using XML mapping for complex IN clause
   */
  List<RCTBTripEntity> findAllTripByCdgTripIdsInPreviousWeeks(
      @Param("tripIds") List<String> tripIds,
      @Param("startDT") String startDT,
      @Param("endDT") String endDT);
}
