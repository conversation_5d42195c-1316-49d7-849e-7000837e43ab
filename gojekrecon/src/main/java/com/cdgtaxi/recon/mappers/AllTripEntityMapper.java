package com.cdgtaxi.recon.mappers;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdgtaxi.recon.inbound.entities.RCTBTripEntity;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
@DS("recsys") // Use recsys datasource for trip data
public interface AllTripEntityMapper extends BaseMapper<RCTBTripEntity> {

  /**
   * Find all trips by start and end time This replaces the JPA native query
   * findAllTripByStartEndTime
   */
  @Select(
      "select t.booking_reference,t.job_no,t.vehicle_no "
          + ",t.job_status,t.entity,t.booked_channel,TO_CHAR(t.completed_dt, 'YYYY-MM-DD') as completed_dt,t.payment_mode,t.total_amount,t.driver_levy,t.refund "
          + ",(t.total_amount-t.driver_levy) as calc_driver_refund "
          + ",ej.dcp_request_id as dcp_request_id "
          + ",'M2-AA2 (payment_to_cdg - total_amount)' as difference "
          + ",'' as REMARKS_ALL "
          + "from recsys.rctb_trip_intf t "
          + "LEFT JOIN recsys.ESC_JOB ej ON t.JOB_NO = ej.JOB_NO "
          + "WHERE 1=1 "
          + "and t.booked_channel='GOJEK' "
          + "and t.job_status='COMPLETED' "
          + "and t.completed_dt>=to_date(#{startDT},'yyyymmdd hh24:mi:ss') "
          + "and t.completed_dt<=to_date(#{endDT},'yyyymmdd hh24:mi:ss')")
  List<RCTBTripEntity> findAllTripByStartEndTime(
      @Param("startDT") String startDT, @Param("endDT") String endDT);

//  /**
//   * Find all trips by CDG trip IDs in previous weeks This replaces the JPA native query
//   * findAllTripByCdgTripIdsInPreviousWeeks Using XML mapper for complex query with foreach
//   */
//  @Select(
//      "select t.booking_reference,t.job_no,t.vehicle_no\n"
//          + "--,t.driver_ic\n"
//          + ",t.job_status,t.entity,t.booked_channel,TO_CHAR(t.completed_dt, 'YYYY-MM-DD') as completed_dt,t.payment_mode,t.total_amount,t.driver_levy,t.refund\n"
//          + ",(t.total_amount-t.driver_levy) as calc_driver_refund\n"
//          + ",ej.dcp_request_id as dcp_request_id\n"
//          + ",'M2-AA2 (payment_to_cdg - total_amount)'difference\n"
//          + ",'' as REMARKS_ALL\n"
//          + "--,t.* \n"
//          + "from recsys.rctb_trip_intf t\n"
//          + "LEFT JOIN recsys.ESC_JOB ej ON t.JOB_NO = ej.JOB_NO\n"
//          + "WHERE 1=1 \n"
//          + "and t.booked_channel='GOJEK'\n"
//          + "and t.job_status='COMPLETED'\n"
//          + "and ej.dcp_request_id in (:tripIds) \n"
//          + "and t.completed_dt>=to_date(:startDT,'yyyymmdd hh24:mi:ss')\n"
//          + "and t.completed_dt<=to_date(:endDT,'yyyymmdd hh24:mi:ss')")
//  List<RCTBTripEntity> findAllTripByCdgTripIdsInPreviousWeeks(
//      @Param("tripIds") List<String> tripIds,
//      @Param("startDT") String startDT,
//      @Param("endDT") String endDT);
}
