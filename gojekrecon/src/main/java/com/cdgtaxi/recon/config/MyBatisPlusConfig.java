package com.cdgtaxi.recon.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

@Configuration
public class MyBatisPlusConfig {

  /** MyBatis Plus interceptor for pagination and other features */
  @Bean
  public MybatisPlusInterceptor mybatisPlusInterceptor() {
    MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
    // Add pagination interceptor for Oracle database
    interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.ORACLE));
    return interceptor;
  }

  /**
   * Custom SqlSessionFactory for dynamic datasource
   * This is needed when we exclude default DataSource auto-configuration
   */
  @Bean
  @Primary
  public SqlSessionFactory sqlSessionFactory(@Autowired DataSource dataSource) throws Exception {
    MybatisSqlSessionFactoryBean factory = new MybatisSqlSessionFactoryBean();
    factory.setDataSource(dataSource);

    // MyBatis Plus configuration
    MybatisConfiguration configuration = new MybatisConfiguration();
    configuration.setMapUnderscoreToCamelCase(true);
    factory.setConfiguration(configuration);

    // Add interceptors
    factory.setPlugins(mybatisPlusInterceptor());

    return factory.getObject();
  }
}
