package com.cdgtaxi.recon.config;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
public class DynamicDataSourceConfig {

    /**
     * Master DataSource (Primary)
     */
    @Bean("master")
    @Primary
    public DataSource masterDataSource() {
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl("******************************= (retry_count=2)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptfspro.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptfspro_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))");
        dataSource.setUsername("CDG_DENG_HUI");
        dataSource.setPassword("Cdg0000278_02");
        dataSource.setDriverClassName("oracle.jdbc.OracleDriver");
        dataSource.setMaximumPoolSize(30);
        dataSource.setMinimumIdle(5);
        dataSource.setConnectionTimeout(30000);
        dataSource.setIdleTimeout(30000);
        dataSource.setMaxLifetime(2000000);
        return dataSource;
    }

    /**
     * Recsys DataSource (for trip data)
     */
    @Bean("recsys")
    public DataSource recsysDataSource() {
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl("******************************= (retry_count=2)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptfspro.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptfspro_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))");
        dataSource.setUsername("CDG_DENG_HUI");
        dataSource.setPassword("Cdg0000278_02");
        dataSource.setDriverClassName("oracle.jdbc.OracleDriver");
        dataSource.setConnectionInitSql("ALTER SESSION SET CURRENT_SCHEMA=recsys");
        dataSource.setMaximumPoolSize(20);
        dataSource.setMinimumIdle(3);
        dataSource.setConnectionTimeout(30000);
        dataSource.setIdleTimeout(30000);
        dataSource.setMaxLifetime(2000000);
        return dataSource;
    }

    /**
     * Archive DataSource (for historical data)
     */
    @Bean("archive")
    public DataSource archiveDataSource() {
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl("******************************= (retry_count=2)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptfspro.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptfspro_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))");
        dataSource.setUsername("CDG_DENG_HUI");
        dataSource.setPassword("Cdg0000278_02");
        dataSource.setDriverClassName("oracle.jdbc.OracleDriver");
        dataSource.setMaximumPoolSize(10);
        dataSource.setMinimumIdle(2);
        dataSource.setConnectionTimeout(30000);
        dataSource.setIdleTimeout(30000);
        dataSource.setMaxLifetime(2000000);
        return dataSource;
    }
}
