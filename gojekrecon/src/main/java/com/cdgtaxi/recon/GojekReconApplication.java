package com.cdgtaxi.recon;

import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;

@EnableCaching
@SpringBootApplication(exclude = {
    org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration.class
})
@EnableEncryptableProperties
@MapperScan(basePackages = "com.cdgtaxi.recon.mappers")
@Slf4j
public class GojekReconApplication implements CommandLineRunner {

  @Autowired GojekReportService gojekReportImportService;

  public GojekReconApplication() {}

  public static void main(String[] args) {
    SpringApplication.run(GojekReconApplication.class, args);
    //    try {
    //      log.info("*Starting GojekReconApplication SERVICE**");
    //      new SpringApplicationBuilder(GojekReconApplication.class)
    //          .listeners(new ApplicationPidFileWriter("gojekrecon.pid"))
    //          .run(args);
    //      log.info("**PAYMENT SERVICE STARTED**");
    //    } catch (Exception e) {
    //      log.error("Error while starting GojekReconApplication SERVICE", e);
    //    }
  }

  public void run(String... args) throws Exception {

    System.out.println("Gojek Recon Application is Ready!");
    //    String path = "/home/<USER>/Downloads/";
    //    String file = "Gojek CDG Recon - 2025-05-19 - 2025-05-25.csv";
    //    gojekReportImportService.handleGojekRecon(path + file, path);
  }
}
