//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.cdgtaxi.recon.inbound.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedNativeQuery;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@NamedNativeQuery(
    name = "findAllTripByStartEndTime",
    query =
        "select t.booking_reference,t.job_no,t.vehicle_no\n"
            + "--,t.driver_ic\n"
            + ",t.job_status,t.entity,t.booked_channel,TO_CHAR(t.completed_dt, 'YYYY-MM-DD') as completed_dt,t.payment_mode,t.total_amount,t.driver_levy,t.refund\n"
            + ",(t.total_amount-t.driver_levy) as calc_driver_refund\n"
            + ",ej.dcp_request_id as dcp_request_id\n"
            + ",'M2-AA2 (payment_to_cdg - total_amount)'difference\n"
            + ",'' as REMARKS_ALL\n"
            + "--,t.* \n"
            + "from recsys.rctb_trip_intf t\n"
            + "LEFT JOIN recsys.ESC_JOB ej ON t.JOB_NO = ej.JOB_NO\n"
            + "WHERE 1=1 \n"
            + "and t.booked_channel='GOJEK'\n"
            + "and t.job_status='COMPLETED'\n"
            + "and t.completed_dt>=to_date(:startDT,'yyyymmdd hh24:mi:ss')\n"
            + "and t.completed_dt<=to_date(:endDT,'yyyymmdd hh24:mi:ss')",
    resultClass = RCTBTripEntity.class)
@NamedNativeQuery(
    name = "findAllTripByCdgTripIdsInPreviousWeeks",
    query =
        "select t.booking_reference,t.job_no,t.vehicle_no\n"
            + "--,t.driver_ic\n"
            + ",t.job_status,t.entity,t.booked_channel,TO_CHAR(t.completed_dt, 'YYYY-MM-DD') as completed_dt,t.payment_mode,t.total_amount,t.driver_levy,t.refund\n"
            + ",(t.total_amount-t.driver_levy) as calc_driver_refund\n"
            + ",ej.dcp_request_id as dcp_request_id\n"
            + ",'M2-AA2 (payment_to_cdg - total_amount)'difference\n"
            + ",'' as REMARKS_ALL\n"
            + "--,t.* \n"
            + "from recsys.rctb_trip_intf t\n"
            + "LEFT JOIN recsys.ESC_JOB ej ON t.JOB_NO = ej.JOB_NO\n"
            + "WHERE 1=1 \n"
            + "and t.booked_channel='GOJEK'\n"
            + "and t.job_status='COMPLETED'\n"
            + "and ej.dcp_request_id in (:tripIds) \n"
            + "and t.completed_dt>=to_date(:startDT,'yyyymmdd hh24:mi:ss')\n"
            + "and t.completed_dt<=to_date(:endDT,'yyyymmdd hh24:mi:ss')",
    resultClass = RCTBTripEntity.class)
@Entity
@Table(name = "rctb_trip_intf")
@Getter
@Setter
@ToString
public class RCTBTripEntity {
  @Column(name = "booking_reference")
  private String bookingReference;

  @Id
  @Column(name = "job_no", nullable = false)
  private String jobNo;

  @Column(name = "vehicle_no")
  private String vehicleNo;

  @Column(name = "job_status")
  private String jobStatus;

  @Column(name = "entity")
  private String entity;

  @Column(name = "booked_channel")
  private String bookedChannel;

  @Column(name = "completed_dt")
  private String completedDt;

  @Column(name = "payment_mode")
  private String paymentMode;

  @Column(name = "total_amount")
  private String totalAmount;

  @Column(name = "driver_levy")
  private String driverLevy;

  @Column(name = "refund")
  private String refund;

  @Column(name = "calc_driver_refund")
  private String calcDriverRefund;

  @Column(name = "dcp_request_id")
  private String dcpRequestId;

  @Column(name = "difference")
  private String difference;

  @Column(name = "remarks_all")
  private String remarksAll;

  public boolean equals(Object o) {
    if (this.bookingReference != null && o instanceof GojekReport gojekReport) {
      return this.bookingReference.equals(gojekReport.getGojekTripId());
    } else if (this.dcpRequestId != null && o instanceof GojekReport gojekReport) {
      return this.dcpRequestId.contains(gojekReport.getCdgTripId());
    } else {
      return super.equals(o);
    }
  }
}
