//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.cdgtaxi.recon.inbound.repositories;

import com.cdgtaxi.recon.inbound.entities.RCTBTripEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface AllTripEntityRepository extends JpaRepository<RCTBTripEntity, String> {
  @Query(nativeQuery = true, name = "findAllTripByStartEndTime")
  List<RCTBTripEntity> findAllTripByStartEndTime(
      @Param("startDT") String startDT, @Param("endDT") String endDT);

  @Query(nativeQuery = true, name = "findAllTripByCdgTripIdsInPreviousWeeks")
  List<RCTBTripEntity> findAllTripByCdgTripIdsInPreviousWeeks(
      @Param("tripIds") List<String> gojekTripIdNotInCdgTripIdsSubList,
      @Param("startDT") String startDT,
      @Param("endDT") String endDT);
}
