//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.cdgtaxi.recon.inbound.models;

import jakarta.persistence.Column;
import lombok.Data;

@Data
public class GojekExcelDTO {
  @Column(name = "mdt_amount")
  private String mdtAmount;

  @Column(name = "base_fare")
  private String baseFare;

  @Column(name = "driver_earning")
  private String driverEarning;

  @Column(name = "cdg_cut")
  private String cdgCut;

  @Column(name = "gojek_cut")
  private String gojekCut;

  @Column(name = "toll")
  private String toll;

  @Column(name = "flash_incentive")
  private String flashIncentive;

  @Column(name = "gojek_trip_id")
  private String gojekTripId;

  @Column(name = "cdg_trip_id")
  private String cdgTripId;

  @Column(name = "trip_date")
  private String tripDate;

  @Column(name = "cdg_vhno")
  private String cdgVhno;

  @Column(name = "cdg_driver_id")
  private String cdgDriverId;

  @Column(name = "payment_to_cdg")
  private String paymentToCdg;
}
