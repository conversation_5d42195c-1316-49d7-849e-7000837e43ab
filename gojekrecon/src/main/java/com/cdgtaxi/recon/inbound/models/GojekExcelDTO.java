//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.cdgtaxi.recon.inbound.models;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class GojekExcelDTO {
  @ExcelProperty("mdt_amount")
  private String mdtAmount;

  @ExcelProperty("base_fare")
  private String baseFare;

  @ExcelProperty("driver_earning")
  private String driverEarning;

  @ExcelProperty("cdg_cut")
  private String cdgCut;

  @ExcelProperty("gojek_cut")
  private String gojekCut;

  @ExcelProperty("toll")
  private String toll;

  @ExcelProperty("flash_incentive")
  private String flashIncentive;

  @ExcelProperty("gojek_trip_id")
  private String gojekTripId;

  @ExcelProperty("cdg_trip_id")
  private String cdgTripId;

  @ExcelProperty("trip_date")
  private String tripDate;

  @ExcelProperty("cdg_vhno")
  private String cdgVhno;

  @ExcelProperty("cdg_driver_id")
  private String cdgDriverId;

  @ExcelProperty("payment_to_cdg")
  private String paymentToCdg;
}
