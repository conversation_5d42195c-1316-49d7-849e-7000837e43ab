package com.cdgtaxi.recon.inbound.models;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.cdgtaxi.recon.inbound.entities.GojekReport;
import com.cdgtaxi.recon.inbound.entities.RCTBTripEntity;
import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import java.math.BigDecimal;
import java.util.Optional;
import lombok.Data;

@Data
@ContentRowHeight(20)
@HeadRowHeight(30)
@ColumnWidth(20)
public class ReportGojekCdgTripDTO {
  @ColumnWidth(10)
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE)
  private Long id;

  @Column(name = "mdt_amount")
  @ExcelProperty({"MDT Amount"})
  private String mdtAmount;

  @Column(name = "base_fare")
  @ExcelProperty({"Base Fare"})
  private String baseFare;

  @Column(name = "driver_earning")
  @ExcelProperty({"Driver Earning"})
  private String driverEarning;

  @Column(name = "cdg_cut")
  @ExcelProperty({"CDG Cut"})
  private String cdgCut;

  @Column(name = "gojek_cut")
  @ExcelProperty({"Gojek Cut"})
  private String gojekCut;

  @ColumnWidth(10)
  @Column(name = "toll")
  @ExcelProperty({"Toll"})
  private String toll;

  @Column(name = "flash_incentive")
  @ExcelProperty({"Flash Incentive"})
  private String flashIncentive;

  @Column(name = "gojek_trip_id")
  @ExcelProperty({"Gojek Trip ID"})
  private String gojekTripId;

  @ColumnWidth(25)
  @Column(name = "cdg_trip_id")
  @ExcelProperty({"CDG Trip ID"})
  private String cdgTripId;

  @Column(name = "trip_date")
  @ExcelProperty({"Trip Date"})
  private String tripDate;

  @Column(name = "cdg_vhno")
  @ExcelProperty({"CDG Vehicle No"})
  private String cdgVhno;

  @Column(name = "cdg_driver_id")
  @ExcelProperty({"CDG Driver ID"})
  private String cdgDriverId;

  @Column(name = "payment_to_cdg")
  @ExcelProperty({"Payment to CDG"})
  private String paymentToCdg;

  @ColumnWidth(25)
  @Column(name = "booking_reference", nullable = false)
  @ExcelProperty({"Booking Reference"})
  private String bookingReference;

  @Column(name = "job_no", nullable = false)
  @ExcelProperty({"Job No"})
  private String jobNo;

  @Column(name = "vehicle_no", nullable = false)
  @ExcelProperty({"Vehicle No"})
  private String vehicleNo;

  @Column(name = "job_status")
  @ExcelProperty({"Job Status"})
  private String jobStatus;

  @Column(name = "entity")
  @ExcelProperty({"Entity"})
  private String entity;

  @Column(name = "booked_channel")
  @ExcelProperty({"Booked Channel"})
  private String bookedChannel;

  @Column(name = "completed_dt")
  @ExcelProperty({"CDG Trip Date"})
  private String completedDt;

  @Column(name = "payment_mode")
  @ExcelProperty({"Payment Mode"})
  private String paymentMode;

  @Column(name = "total_amount")
  @ExcelProperty({"Total Amount"})
  private String totalAmount;

  @Column(name = "driver_levy")
  @ExcelProperty({"Driver Levy"})
  private String driverLevy;

  @Column(name = "refund")
  @ExcelProperty({"Refund"})
  private String refund;

  @Column(name = "calc_driver_refund")
  @ExcelProperty({"Calc Driver Refund"})
  private String calcDriverRefund;

  @Column(name = "dcp_reqeust_id")
  @ExcelProperty({"DCP Request ID"})
  private String dcpRequestId;

  @Column(name = "difference")
  @ExcelProperty({"Difference"})
  private String difference;

  @Column(name = "remarks_all")
  @ExcelProperty({"Remarks All"})
  private String remarksAll;

  public ReportGojekCdgTripDTO() {}

  public void setValuesByGojekReport(GojekReport gojekReport) {
    this.id = gojekReport.getId();
    this.mdtAmount = gojekReport.getMdtAmount();
    this.baseFare = gojekReport.getBaseFare();
    this.driverEarning = gojekReport.getDriverEarning();
    this.cdgCut = gojekReport.getCdgCut();
    this.gojekCut = gojekReport.getGojekCut();
    this.toll = gojekReport.getToll();
    this.flashIncentive = gojekReport.getFlashIncentive();
    this.gojekTripId = gojekReport.getGojekTripId();
    this.cdgTripId = gojekReport.getCdgTripId();
    this.tripDate = gojekReport.getTripDate();
    this.cdgVhno = gojekReport.getCdgVhno();
    this.cdgDriverId = gojekReport.getCdgDriverId();
    this.paymentToCdg = gojekReport.getPaymentToCdg();
  }

  public void setValuesByCdgTrip(Optional<RCTBTripEntity> cdgTripEntityOptional) {
    if (cdgTripEntityOptional.isPresent()) {
      RCTBTripEntity cdgTripEntity = (RCTBTripEntity) cdgTripEntityOptional.get();
      this.bookingReference = cdgTripEntity.getBookingReference();
      this.jobNo = cdgTripEntity.getJobNo();
      this.vehicleNo = cdgTripEntity.getVehicleNo();
      this.jobStatus = cdgTripEntity.getJobStatus();
      this.entity = cdgTripEntity.getEntity();
      this.bookedChannel = cdgTripEntity.getBookedChannel();
      this.completedDt = cdgTripEntity.getCompletedDt();
      this.paymentMode = cdgTripEntity.getPaymentMode();
      this.totalAmount = cdgTripEntity.getTotalAmount();
      this.driverLevy = cdgTripEntity.getDriverLevy();
      this.refund = cdgTripEntity.getRefund();
      this.calcDriverRefund = cdgTripEntity.getCalcDriverRefund();
      this.dcpRequestId = cdgTripEntity.getDcpRequestId();
      if (this.paymentToCdg == null) {
        this.difference = "N/A";
        this.remarksAll = "Not in Gojek List";
      } else {
        BigDecimal paymentToCdg = new BigDecimal(this.paymentToCdg);
        this.difference =
            this.totalAmount != null
                ? paymentToCdg.subtract(new BigDecimal(this.totalAmount)).toString()
                : "N/A";
        this.remarksAll = "Booking ID Tally";
        this.remarksAll =
            this.paymentMode.equalsIgnoreCase("CASH") ? "Cash Payment" : this.remarksAll;
      }
    } else {
      this.remarksAll = "Not in CDG list";
    }
  }
}
