package com.cdgtaxi.recon;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.cdgtaxi.recon.inbound.entities.RCTBTripEntity;
import com.cdgtaxi.recon.mappers.AllTripEntityMapper;
import com.cdgtaxi.recon.mappers.GojekReportMapper;
import com.cdgtaxi.recon.utils.CsvService;
import com.cdgtaxi.recon.utils.ExcelService;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class GojekReportServiceTest {

  @Mock private AllTripEntityMapper allTripEntityMapper;
  @Mock private GojekReportMapper gojekReportMapper;
  @Mock private CsvService csvService;
  @Mock private ExcelService excelService;

  private GojekReportService gojekReportService;

  @BeforeEach
  void setUp() {
    gojekReportService =
        new GojekReportService(allTripEntityMapper, gojekReportMapper, csvService, excelService);
  }

  @Test
  void testServiceInitialization() {
    // Test that the service is properly initialized with MyBatis Plus mappers
    assertNotNull(gojekReportService);
    assertNotNull(allTripEntityMapper);
    assertNotNull(gojekReportMapper);
    assertNotNull(csvService);
    assertNotNull(excelService);
  }

  @Test
  void testFindAllTripByStartEndTime() {
    // Mock the mapper to return empty list
    List<RCTBTripEntity> mockTripList = new ArrayList<>();
    when(allTripEntityMapper.findAllTripByStartEndTime(anyString(), anyString()))
        .thenReturn(mockTripList);

    // Test the mapper call
    List<RCTBTripEntity> result =
        allTripEntityMapper.findAllTripByStartEndTime("20241101 00:00:00", "20241107 23:59:59");
    assertNotNull(result);
    assertEquals(0, result.size());
  }

  @Test
  void handleGojekRecon() {
    


  }
}
