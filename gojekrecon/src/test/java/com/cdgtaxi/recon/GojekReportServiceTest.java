package com.cdgtaxi.recon;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest(classes = GojekReconApplication.class)
@ActiveProfiles("prd")
class GojekReportServiceTest {

  @Autowired GojekReportService gojekReportImportService;

  @Test
  void handleGojekRecon() {
    // get user home directory
    String userHome = System.getProperty("user.home");
    String path = userHome + "/Downloads/";
    String file = "Gojek_CDG_Recon_2024-11-11_2024-11-17.xlsx";
    gojekReportImportService.handleGojekRecon(path + file, path);
    assertNotNull(gojekReportImportService);
  }
}
