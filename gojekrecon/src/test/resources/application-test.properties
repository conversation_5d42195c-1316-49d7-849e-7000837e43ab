# Test configuration - no encryption needed
spring.application.name=recon-test
server.port=9902

# Exclude DataSource auto-configuration for dynamic datasources
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration

# Dynamic DataSource Configuration for Tests
spring.datasource.dynamic.primary=master
spring.datasource.dynamic.strict=false

# Test Database configuration - H2 in-memory for tests
spring.datasource.dynamic.datasource.master.driver-class-name=org.h2.Driver
spring.datasource.dynamic.datasource.master.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.dynamic.datasource.master.username=sa
spring.datasource.dynamic.datasource.master.password=

# Secondary test datasource
spring.datasource.dynamic.datasource.recsys.driver-class-name=org.h2.Driver
spring.datasource.dynamic.datasource.recsys.url=jdbc:h2:mem:testdb2;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.dynamic.datasource.recsys.username=sa
spring.datasource.dynamic.datasource.recsys.password=

# Archive test datasource
spring.datasource.dynamic.datasource.archive.driver-class-name=org.h2.Driver
spring.datasource.dynamic.datasource.archive.url=jdbc:h2:mem:testdb3;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.dynamic.datasource.archive.username=sa
spring.datasource.dynamic.datasource.archive.password=

# MyBatis Plus configuration for tests
mybatis-plus.mapper-locations=classpath:mapper/*.xml
mybatis-plus.type-aliases-package=com.cdgtaxi.recon.inbound.entities
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# Disable Jasypt for tests
jasypt.encryptor.password=test
