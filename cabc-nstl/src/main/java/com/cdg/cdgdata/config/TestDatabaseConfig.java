package com.cdg.cdgdata.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

/**
 * Test database configuration that provides basic JdbcTemplate beans
 * for testing with H2 database when the main DatabaseConfig is disabled
 */
//@Configuration  // Disabled - using real Oracle databases
//@Profile("test")
public class TestDatabaseConfig {

    @Autowired
    private DataSource dataSource;

    @Bean(name = "ptfsproJdbcTemplate")
    public JdbcTemplate ptfsproJdbcTemplate() {
        return new JdbcTemplate(dataSource);
    }

    @Bean(name = "ptfsproIbsJdbcTemplate")
    public JdbcTemplate ptfsproIbsJdbcTemplate() {
        return new JdbcTemplate(dataSource);
    }

    @Bean(name = "ptccproJdbcTemplate")
    public JdbcTemplate ptccproJdbcTemplate() {
        return new JdbcTemplate(dataSource);
    }

    @Bean(name = "ptccproDcpJdbcTemplate")
    public JdbcTemplate ptccproDcpJdbcTemplate() {
        return new JdbcTemplate(dataSource);
    }

    @Bean(name = "ptccproTmJdbcTemplate")
    public JdbcTemplate ptccproTmJdbcTemplate() {
        return new JdbcTemplate(dataSource);
    }

    @Bean(name = "ptccproPayJdbcTemplate")
    public JdbcTemplate ptccproPayJdbcTemplate() {
        return new JdbcTemplate(dataSource);
    }

    @Bean(name = "ptcnuat1JdbcTemplate")
    public JdbcTemplate ptcnuat1JdbcTemplate() {
        return new JdbcTemplate(dataSource);
    }

    @Bean(name = "ptdaproJdbcTemplate")
    public JdbcTemplate ptdaproJdbcTemplate() {
        return new JdbcTemplate(dataSource);
    }

    @Bean(name = "ptcnuat2JdbcTemplate")
    public JdbcTemplate ptcnuat2JdbcTemplate() {
        return new JdbcTemplate(dataSource);
    }

    @Bean(name = "ptdaproDcpJdbcTemplate")
    public JdbcTemplate ptdaproDcpJdbcTemplate() {
        return new JdbcTemplate(dataSource);
    }
}
